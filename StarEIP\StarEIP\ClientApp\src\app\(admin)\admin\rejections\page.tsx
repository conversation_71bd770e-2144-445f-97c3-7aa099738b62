"use client";

import { useState, useRef, useEffect } from "react";
import DataGrid, {
  <PERSON>um<PERSON>,
  <PERSON>r,
  Paging,
  SearchPanel,
  <PERSON>er<PERSON><PERSON><PERSON>,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Toolbar,
  Item,
  DataGridRef,
  FilterRow,
  GroupPanel,
  Summary,
  TotalItem,
  Selection,
} from "devextreme-react/data-grid";
import { createStore, CustomStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import { useRouter } from "next/navigation";
import dataGridExport from "@/app/Utils/dataGridExport";
import Button from "devextreme-react/button";
import {
  Modal,
  TextInput,
  Button as MantineButton,
  Group,
  Tooltip,
  ActionIcon,
  Text,
} from "@mantine/core";
import {
  IconSearch,
  IconLinkPlus,
  IconBabyCarriage,
} from "@tabler/icons-react";
import axios from "axios";
import { notifications } from "@mantine/notifications";
import ChildMappingDrawer from "../patients/components/ChildMappingDrawer";
import TaskDetailsDrawer from "../taskitems/components/TaskDetailsDrawer";
import CreateTaskDrawer from "../taskitems/components/CreateTaskDrawer";
import PatientDetailsDrawer from "../patients/components/PatientDetailsDrawer";

const serviceUrl = urlHelpers.getAbsoluteURL("api/rejections");

interface RejectionRow {
  id: number;
  childName: string;
  eiNumber: string;
  provider: string;
  billAmount: number;
  adjustReasonCodes: string;
  childId: number | null;
  childFirstName: string | null;
  childLastName: string | null;
  taskId: number | null;
  taskTitle: string | null;
  taskStatusId: number | null;
  taskStatusName: string | null;
}

// Function to get common childId from selected rows
function getCommonChildId(
  selectedRowKeys: any[],
  dataGrid: DataGridRef<any, any>,
): number | null {
  if (selectedRowKeys.length === 0) return null;

  try {
    const selectedRowsData = dataGrid.instance().getSelectedRowsData() || [];
    if (selectedRowsData.length !== selectedRowKeys.length) return null;

    const childIds = selectedRowsData
      .map((row: any) => row.childId)
      .filter((id: any) => id != null);
    if (childIds.length !== selectedRowKeys.length) return null;

    const uniqueChildIds = Array.from(new Set(childIds));
    return uniqueChildIds.length === 1 ? uniqueChildIds[0] : null;
  } catch (error) {
    console.error("Error getting common childId:", error);
    return null;
  }
}

const RejectionsPage = () => {
  const [remoteDataSource, setRemoteDataSource] = useState<CustomStore>();
  const router = useRouter();
  const dataGridRef = useRef<DataGridRef<any, any>>(null);
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [linkTaskModalOpen, setLinkTaskModalOpen] = useState(false);
  const [linkMultipleTaskModalOpen, setLinkMultipleTaskModalOpen] =
    useState(false);
  const [taskId, setTaskId] = useState<string>("");
  const [selectedRejectionId, setSelectedRejectionId] = useState<number | null>(
    null,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const loadRejectionsDataSource = async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        onBeforeSend: (_, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          if (e.message === "Unauthorized") {
            router.push("/login");
          }
        },
      }),
    );
  };

  useEffect(() => {
    loadRejectionsDataSource();
  }, []);

  const toggleFilter = () => {
    setIsFilterVisible(!isFilterVisible);
  };

  const handleSelectionChanged = (e: any) => {
    setSelectedRowKeys(e.selectedRowKeys);
  };

  const handleTaskCreationSuccess = () => {
    // Reset the DataGrid selection
    setSelectedRowKeys([]);
    const grid = dataGridRef.current?.instance();
    if (grid) {
      grid.clearSelection();
    }

    // Refresh the data
    loadRejectionsDataSource();
  };

  const openLinkTaskModal = (rejectionId: number) => {
    setSelectedRejectionId(rejectionId);
    setTaskId("");
    setLinkTaskModalOpen(true);
  };

  const handleLinkToTask = async () => {
    if (!taskId.trim() || !selectedRejectionId) {
      notifications.show({
        title: "Validation Error",
        message: "Task ID is required",
        color: "red",
      });
      return;
    }

    const taskIdNumber = parseInt(taskId);
    if (isNaN(taskIdNumber)) {
      notifications.show({
        title: "Validation Error",
        message: "Task ID must be a number",
        color: "red",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await axios.post(
        urlHelpers.getAbsoluteURL(`api/taskitems/${taskIdNumber}/link`),
        {
          tableName: "Import_ReconciliationEntry",
          itemId: selectedRejectionId,
          linkType: "Rejection",
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          },
        },
      );

      notifications.show({
        title: "Success",
        message: response.data.message,
        color: "green",
      });
      setLinkTaskModalOpen(false);
      loadRejectionsDataSource(); // Refresh the data
    } catch (error: any) {
      console.error("Error linking to task:", error);
      notifications.show({
        title: "Error",
        message: error.response?.data?.message || "Failed to link to task",
        color: "red",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openLinkMultipleTaskModal = () => {
    if (selectedRowKeys.length === 0) {
      notifications.show({
        title: "Selection Required",
        message: "Please select at least one rejection to link to a task",
        color: "yellow",
      });
      return;
    }
    setTaskId("");
    setLinkMultipleTaskModalOpen(true);
  };

  const handleLinkMultipleToTask = async () => {
    if (!taskId.trim()) {
      notifications.show({
        title: "Validation Error",
        message: "Task ID is required",
        color: "red",
      });
      return;
    }

    const taskIdNumber = parseInt(taskId);
    if (isNaN(taskIdNumber)) {
      notifications.show({
        title: "Validation Error",
        message: "Task ID must be a number",
        color: "red",
      });
      return;
    }

    if (selectedRowKeys.length === 0) {
      notifications.show({
        title: "Selection Required",
        message: "Please select at least one rejection to link to a task",
        color: "yellow",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await axios.post(
        urlHelpers.getAbsoluteURL(
          `api/taskitems/${taskIdNumber}/link-multiple`,
        ),
        {
          items: selectedRowKeys.map((id) => ({
            tableName: "Import_ReconciliationEntry",
            itemId: id,
            linkType: "Rejection",
          })),
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          },
        },
      );

      notifications.show({
        title: "Success",
        message: response.data.message,
        color: "green",
      });
      setLinkMultipleTaskModalOpen(false);
      setSelectedRowKeys([]);

      // Reset the DataGrid selection
      const grid = dataGridRef.current?.instance();
      if (grid) {
        grid.clearSelection();
      }

      loadRejectionsDataSource(); // Refresh the data
    } catch (error: any) {
      console.error("Error linking multiple items to task:", error);
      notifications.show({
        title: "Error",
        message:
          error.response?.data?.message || "Failed to link items to task",
        color: "red",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Modal
        opened={linkTaskModalOpen}
        onClose={() => setLinkTaskModalOpen(false)}
        title="Link to Existing Task"
        size="sm"
      >
        <div style={{ display: "flex", flexDirection: "column", gap: "1rem" }}>
          <TextInput
            label="Task ID"
            placeholder="Enter task ID"
            value={taskId}
            onChange={(e) => setTaskId(e.target.value)}
            required
          />
          <Group justify="flex-end" mt="md">
            <MantineButton
              variant="outline"
              onClick={() => setLinkTaskModalOpen(false)}
            >
              Cancel
            </MantineButton>
            <MantineButton
              onClick={handleLinkToTask}
              loading={isSubmitting}
              leftSection={<IconLinkPlus size="1rem" />}
            >
              Link to Task
            </MantineButton>
          </Group>
        </div>
      </Modal>

      <Modal
        opened={linkMultipleTaskModalOpen}
        onClose={() => setLinkMultipleTaskModalOpen(false)}
        title="Link Multiple Rejections to Existing Task"
        size="md"
      >
        <div style={{ display: "flex", flexDirection: "column", gap: "1rem" }}>
          <Text>
            You are about to link {selectedRowKeys.length} rejection(s) to an
            existing task.
          </Text>
          <TextInput
            label="Task ID"
            placeholder="Enter task ID"
            value={taskId}
            onChange={(e) => setTaskId(e.target.value)}
            required
          />
          <Group justify="flex-end" mt="md">
            <MantineButton
              variant="outline"
              onClick={() => setLinkMultipleTaskModalOpen(false)}
            >
              Cancel
            </MantineButton>
            <MantineButton
              onClick={handleLinkMultipleToTask}
              loading={isSubmitting}
              leftSection={<IconLinkPlus size="1rem" />}
            >
              Link to Task
            </MantineButton>
          </Group>
        </div>
      </Modal>

      <DataGrid
        remoteOperations
        ref={dataGridRef}
        dataSource={remoteDataSource}
        onExporting={dataGridExport}
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        columnResizingMode={"widget"}
        showColumnLines
        twoWayBindingEnabled
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onSelectionChanged={handleSelectionChanged}
      >
        <Selection mode="multiple" />
        <Toolbar>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item location="before">
            <Button icon="refresh" onClick={loadRejectionsDataSource} />
          </Item>
          <Item location="before">
            <Button
              icon="filter"
              type={isFilterVisible ? "default" : "normal"}
              onClick={toggleFilter}
            />
          </Item>
          <Item location="before">
            <div style={{ marginLeft: "8px" }}>
              <CreateTaskDrawer
                linkedItems={selectedRowKeys.map((id) => ({
                  tableName: "Import_ReconciliationEntry",
                  itemId: id,
                  linkType: "Rejection",
                }))}
                onSuccess={handleTaskCreationSuccess}
                buttonText="Create Task"
                defaultTitle={`Rejection Task - ${new Date().toLocaleDateString()}`}
                disabled={selectedRowKeys.length === 0}
                childId={
                  selectedRowKeys.length > 0 && dataGridRef.current
                    ? getCommonChildId(selectedRowKeys, dataGridRef.current)
                    : null
                }
              />
            </div>
          </Item>
          <Item location="before">
            <Button
              icon="link"
              text="Link to Task"
              onClick={openLinkMultipleTaskModal}
              disabled={selectedRowKeys.length === 0}
            />
          </Item>
          <Item name="groupPanel" locateInMenu="auto" location="before" />
          <Item name="exportButton" locateInMenu="auto" location="after" />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
        </Toolbar>

        <GroupPanel visible={true} />

        <Column dataField="id" caption="ID" width={70} visible={false} />
        <Column dataField="childName" caption="Child Name" />
        <Column
          dataField="eiNumber"
          caption="EI Number"
          cellRender={(data) => {
            const row = data.data as RejectionRow;
            return (
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <span>{row.eiNumber}</span>
                {row.childId ? (
                  <div onClick={(e) => e.stopPropagation()}>
                    <Tooltip
                      label={`View details for ${row.childFirstName} ${row.childLastName}`}
                      position="right"
                      withArrow
                    >
                      <div>
                        <PatientDetailsDrawer
                          childId={row.childId}
                          buttonSize="xs"
                          buttonText={<IconBabyCarriage size="0.8rem" />}
                        />
                      </div>
                    </Tooltip>
                  </div>
                ) : (
                  <div onClick={(e) => e.stopPropagation()}>
                    <Tooltip label="Map to a child" position="right" withArrow>
                      <div>
                        <ChildMappingDrawer
                          eiNumber={row.eiNumber}
                          onMappingSuccess={loadRejectionsDataSource}
                          buttonSize="xs"
                          buttonText={<IconSearch size="0.8rem" />}
                          enableDataLookup={true}
                        />
                      </div>
                    </Tooltip>
                  </div>
                )}
              </div>
            );
          }}
        />
        <Column
          dataField="taskId"
          caption="Task ID"
          cellRender={(data) => {
            const row = data.data as RejectionRow;
            return (
              <div
                onClick={(e) => e.stopPropagation()}
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                {row.taskId ? (
                  <TaskDetailsDrawer
                    taskId={row.taskId}
                    buttonText={`#${row.taskId}`}
                    buttonSize="xs"
                    onTaskUpdated={loadRejectionsDataSource}
                    useHyperlink={true}
                  />
                ) : (
                  <Tooltip
                    label="Link to existing task"
                    position="right"
                    withArrow
                  >
                    <ActionIcon
                      color="blue"
                      variant="subtle"
                      size="sm"
                      onClick={() => openLinkTaskModal(row.id)}
                    >
                      <IconLinkPlus size="1rem" />
                    </ActionIcon>
                  </Tooltip>
                )}
              </div>
            );
          }}
        />
        <Column dataField="taskStatusName" caption="Task Status" />
        <Column dataField="provider" caption="Provider" />
        <Column
          dataField="sessionDate"
          caption="Session Date"
          dataType="date"
          format="MM/dd/yyyy"
          sortIndex={0}
          sortOrder="asc"
        />
        <Column
          dataField="claimCreated"
          caption="Claim Created"
          dataType="date"
          format="MM/dd/yyyy"
        />
        <Column dataField="type" caption="Type" />
        <Column dataField="subType" caption="Sub Type" visible={false} />
        <Column
          dataField="billAmount"
          caption="Bill Amount"
          dataType="number"
          format="currency"
          alignment="right"
          allowGrouping={true}
          allowFiltering={true}
        />
        <Column dataField="remittStatus" caption="Remitt Status" />
        <Column
          dataField="remittDate"
          caption="Remitt Date"
          dataType="date"
          format="MM/dd/yyyy"
        />
        <Column
          dataField="paymentAmount"
          caption="Payment Amount"
          dataType="number"
          format="currency"
          alignment="right"
        />
        <Column dataField="fundingSource" caption="Funding Source" />
        <Column
          dataField="discrepancy"
          caption="Discrepancy"
          dataType="number"
          format="currency"
          alignment="right"
        />
        <Column dataField="adjustReasonCodes" caption="Adjust Reason Codes" />
        <Column
          dataField="adjustReasonDescription"
          caption="Adjust Reason Description"
          width={300}
        />
        <Column dataField="reconciled" caption="Reconciled" />

        <SearchPanel visible={true} width={240} placeholder="Search..." />
        <HeaderFilter visible={true} />
        <ColumnChooser enabled={true} />
        <Sorting mode="multiple" />
        <ColumnFixing enabled={true} />
        <FilterPanel visible={isFilterVisible} />
        <FilterRow visible={isFilterVisible} />
        <FilterBuilderPopup position={{ my: "top", at: "top", of: window }} />
        <Export enabled={true} allowExportSelectedData={true} />
        <Paging defaultPageSize={50} />
        <Pager
          showPageSizeSelector={true}
          allowedPageSizes={[50, 100, 250, 500]}
          showInfo={true}
          showNavigationButtons
          infoText="Page {0} of {1} (Total Rows: {2})"
        />

        <Summary>
          <TotalItem
            column="billAmount"
            summaryType="sum"
            valueFormat="currency"
            displayFormat="Total: {0}"
          />
          <TotalItem
            column="paymentAmount"
            summaryType="sum"
            valueFormat="currency"
            displayFormat="Total: {0}"
          />
          <TotalItem
            column="discrepancy"
            summaryType="sum"
            valueFormat="currency"
            displayFormat="Total: {0}"
          />
        </Summary>
      </DataGrid>
    </>
  );
};

export default RejectionsPage;
